# yaml-language-server: $schema=https://promptfoo.dev/config-schema.json

# Learn more about building a configuration: https://promptfoo.dev/docs/configuration/guide

description: "Evaluaciones para Tech Lead Advisor"

prompts:
  - "Write a tweet about {{topic}}"
  

providers:
  - id: openai:gpt-4o-mini
    config:
      apiKey: ********************************************************************************************************************************************************************

  - id: openai:gpt-4o
    config:
      apiKey: ********************************************************************************************************************************************************************

  - id: anthropic:messages:claude-sonnet-4-20250514
    config:
      apiKey: ************************************************************************************************************

  - id: google:gemini-2.5-flash
    config:
      apiKey: AIzaSyBVPqpPymSRRy5SAIUEEIV6VS05kTEDy-c

defaultTest:
  options:
    provider: openai:gpt-4o-mini

tests:
  - vars:
      topic: bananas

  - vars:
      topic: avocado toast
    assert:
      # For more information on assertions, see https://promptfoo.dev/docs/configuration/expected-outputs

      # Make sure output contains the word "avocado"
      - type: icontains
        value: avocado

      # Prefer shorter outputs
      - type: javascript
        value: 1 / (output.length + 1)

  - vars:
      topic: new york city
    assert:
      - type: javascript
        value: |
          // Check for humor indicators in NYC tweets
          const text = output.toLowerCase();

          // Humor indicators
          const humorWords = ['😂', '🤣', 'lol', 'haha', 'funny', 'joke', 'wit', 'clever', 'hilarious'];
          const hasHumorEmoji = humorWords.some(word => output.includes(word));

          // NYC-specific humor patterns
          const nycHumorPatterns = [
            /pizza.*slice/i,
            /subway.*delay/i,
            /traffic.*taxi/i,
            /rent.*expensive/i,
            /bodega.*cat/i,
            /empire.*state/i,
            /brooklyn.*hipster/i,
            /manhattan.*crowded/i,
            /central.*park/i
          ];

          const hasNYCHumor = nycHumorPatterns.some(pattern => pattern.test(output));

          // Playful language indicators
          const playfulWords = ['amazing', 'incredible', 'wild', 'crazy', 'epic', 'legendary'];
          const hasPlayfulLanguage = playfulWords.some(word => text.includes(word));

          // NYC references
          const nycReferences = ['nyc', 'new york', 'manhattan', 'brooklyn', 'queens', 'bronx', 'staten island'];
          const hasNYCReference = nycReferences.some(ref => text.includes(ref));

          // Scoring: needs NYC reference + at least one humor indicator
          const score = (hasNYCReference ? 0.4 : 0) +
                       (hasHumorEmoji ? 0.3 : 0) +
                       (hasNYCHumor ? 0.4 : 0) +
                       (hasPlayfulLanguage ? 0.2 : 0);

          return Math.min(score, 1.0);



