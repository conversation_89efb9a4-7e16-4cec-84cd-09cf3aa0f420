# yaml-language-server: $schema=https://promptfoo.dev/config-schema.json

# Learn more about building a configuration: https://promptfoo.dev/docs/configuration/guide

description: "Evaluaciones para Tech Lead Advisor"

prompts:
  - "Write a tweet about {{topic}}"
  

providers:
  - id: openai:gpt-4o-mini
    config:
      apiKey: ********************************************************************************************************************************************************************

  - id: openai:gpt-4o
    config:
      apiKey: ********************************************************************************************************************************************************************

  - id: anthropic:messages:claude-sonnet-4-20250514
    config:
      apiKey: ************************************************************************************************************

  - id: google:gemini-2.5-flash
    config:
      apiKey: AIzaSyBVPqpPymSRRy5SAIUEEIV6VS05kTEDy-c

defaultTest:
  options:
    provider: openai:gpt-4o-mini

tests:
  - vars:
      topic: bananas

  - vars:
      topic: avocado toast
    assert:
      # For more information on assertions, see https://promptfoo.dev/docs/configuration/expected-outputs

      # Make sure output contains the word "avocado"
      - type: icontains
        value: avocado

      # Prefer shorter outputs
      - type: javascript
        value: 1 / (output.length + 1)

  - vars:
      topic: new york city
    options:
      provider: openai:gpt-4o-mini
    assert:
      - type: llm-rubric
        value: ensure that the output is funny



