# yaml-language-server: $schema=https://promptfoo.dev/config-schema.json

# Learn more about building a configuration: https://promptfoo.dev/docs/configuration/guide

description: "Evaluaciones para Tech Lead Advisor"

prompts:
  - "Write a tweet about {{topic}}"
  

providers:
  - id: openai:gpt-4o-mini
    config:
      apiKey: ********************************************************************************************************************************************************************

  - id: openai:gpt-4o
    config:
      apiKey: ********************************************************************************************************************************************************************

  - id: anthropic:messages:claude-sonnet-4-20250514
    config:
      apiKey: ************************************************************************************************************

  - id: google:gemini-2.5-flash
    config:
      apiKey: AIzaSyBVPqpPymSRRy5SAIUEEIV6VS05kTEDy-c

  # Provider específico para evaluaciones
  - id: evaluator:openai:gpt-4o-mini
    config:
      apiKey: ********************************************************************************************************************************************************************

defaultTest:
  options:
    provider: openai:gpt-4o-mini

tests:
  - vars:
      topic: bananas

  - vars:
      topic: avocado toast
    assert:
      # For more information on assertions, see https://promptfoo.dev/docs/configuration/expected-outputs

      # Make sure output contains the word "avocado"
      - type: icontains
        value: avocado

      # Prefer shorter outputs
      - type: javascript
        value: 1 / (output.length + 1)

  - vars:
      topic: new york city
    assert:
      - type: model-graded
        value: |
          You are evaluating a tweet about New York City for humor content.

          Rate the tweet on a scale of 1-10 for humor, where:
          - 1-3: Not funny at all
          - 4-6: Mildly amusing
          - 7-8: Quite funny
          - 9-10: Very funny/hilarious

          Consider these humor elements:
          - Wordplay or puns
          - Clever observations about NYC life
          - Funny references to NYC culture (pizza, subway, traffic, rent, etc.)
          - Witty use of emojis
          - Amusing contrasts or comparisons

          Tweet to evaluate: {{output}}

          Respond with just a number from 1-10.
        provider: evaluator:openai:gpt-4o-mini
        threshold: 6



